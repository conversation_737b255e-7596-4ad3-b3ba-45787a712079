<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="姓名" prop="name">
        <el-input
            v-model="queryParams.name"
            placeholder="请输入姓名"
            clearable
            @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="身份证号" prop="idCard">
        <el-input
            v-model="queryParams.idCard"
            placeholder="请输入身份证号"
            clearable
            @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="性别" prop="sex">
        <el-select v-model="queryParams.sex" placeholder="请选择性别" clearable>
          <el-option
              v-for="dict in sys_user_sex"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="申请类型" prop="applyType">
        <el-select v-model="queryParams.applyType" placeholder="请选择申请类型" clearable>
          <el-option
              v-for="dict in apply_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="审批状态" prop="applyStatus">
        <el-select v-model="queryParams.applyStatus" placeholder="请选择审批状态" clearable>
          <el-option
              v-for="dict in apply_status"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="领取方式" prop="receiveType">
        <el-select v-model="queryParams.receiveType" placeholder="请选择领取方式" clearable>
          <el-option
              v-for="dict in receive_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
            v-model="queryParams.timeRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            clearable
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
<!--      <el-col :span="1.5">
        <el-button
            type="primary"
            plain
            icon="Plus"
            @click="handleAdd"
            v-hasPermi="['bizz:apply:add']"
        >新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="success"
            plain
            icon="Edit"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['bizz:apply:edit']"
        >修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="danger"
            plain
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['bizz:apply:remove']"
        >删除
        </el-button>
      </el-col>-->
      <el-col :span="1.5">
        <el-button
            type="warning"
            plain
            icon="Download"
            @click="handleExport"
            v-hasPermi="['bizz:apply:export']"
        >导出
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Check"
          :disabled="!allSelectedStatus2"
          @click="handleApprove(null,'pass')"
          v-hasPermi="['bizz:apply:edit']"
        >批量通过</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Close"
          :disabled="!allSelectedStatus2"
          @click="handleApprove(null,'reject')"
          v-hasPermi="['bizz:apply:edit']"
        >批量拒绝</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="applyList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="主键" align="center" prop="id"/>
      <el-table-column label="姓名" align="center" prop="name"/>
      <el-table-column label="身份证号" align="center" prop="idCard"/>
      <el-table-column label="手机号码" align="center" prop="phone"/>
      <el-table-column label="性别" align="center" prop="sex">
        <template #default="scope">
          <dict-tag :options="sys_user_sex" :value="scope.row.sex"/>
        </template>
      </el-table-column>
      <el-table-column label="学历" align="center" prop="education">
        <template #default="scope">
          <dict-tag :options="education_option" :value="scope.row.education"/>
        </template>
      </el-table-column>
      <el-table-column label="工作单位" align="center" prop="company"/>
      <el-table-column label="二寸照片" align="center" prop="photoUrl" width="100">
        <template #default="scope">
          <image-preview :src="scope.row.photoUrl" :width="50" :height="50"/>
<!--          <image-preview src="https://drxin-1359893946.cos.ap-shanghai.myqcloud.com/drxin-project/image/2025/06/01/4b3446cc-d8bd-42c1-88b5-1113aed9922f.jpg" :width="50" :height="50"/>-->
        </template>
      </el-table-column>
      <el-table-column label="海姆利克实操照片" align="center" prop="heimlichUrl" width="100">
        <template #default="scope">
          <image-preview :src="scope.row.heimlichUrl" :width="50" :height="50"/>
        </template>
      </el-table-column>
      <el-table-column label="考试截图" align="center" prop="examUrl" width="100">
        <template #default="scope">
          <image-preview :src="scope.row.examUrl" :width="50" :height="50"/>
        </template>
      </el-table-column>
      <el-table-column label="申请类型" align="center" prop="applyType">
        <template #default="scope">
          <dict-tag :options="apply_type" :value="scope.row.applyType"/>
        </template>
      </el-table-column>
      <el-table-column label="审批状态" align="center" prop="applyStatus">
        <template #default="scope">
          <dict-tag :options="apply_status" :value="scope.row.applyStatus"/>
        </template>
      </el-table-column>
      <el-table-column label="领取方式" align="center" prop="receiveType">
        <template #default="scope">
          <dict-tag :options="receive_type" :value="scope.row.receiveType"/>
        </template>
      </el-table-column>
      <el-table-column label="收件地址" align="center" prop="receiveAddress"/>
      <el-table-column label="是否实操" align="center" prop="practiceFlag">
        <template #default="scope">
          <dict-tag :options="sys_yes_no" :value="scope.row.practiceFlag"/>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
<!--          <el-button
              type="text"
              icon="Edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['bizz:apply:edit']"
          >修改
          </el-button>
          <el-button
              type="text"
              icon="Delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['bizz:apply:remove']"
          >删除
          </el-button>-->
          <el-button
            type="primary"
            text
            size="small"
            :disabled="scope.row.applyStatus != 2"
            @click="handleApprove(scope.row.id, 'pass')"
          >通过</el-button>
          <el-button
            type="primary"
            text
            size="small"
            :disabled="scope.row.applyStatus != 2"
            @click="handleApprove(scope.row.id, 'reject')"
          >拒绝</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
        v-show="total>0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
    />

    <!-- 添加或修改证件申请对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="applyRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="姓名" prop="name">
          <el-input v-model="form.name" placeholder="请输入姓名"/>
        </el-form-item>
        <el-form-item label="身份证号" prop="idCard">
          <el-input v-model="form.idCard" placeholder="请输入身份证号"/>
        </el-form-item>
        <el-form-item label="手机号码" prop="phone">
          <el-input v-model="form.phone" placeholder="请输入手机号码"/>
        </el-form-item>
        <el-form-item label="性别" prop="sex">
          <el-select v-model="form.sex" placeholder="请选择性别">
            <el-option
                v-for="dict in sys_user_sex"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="学历" prop="education">
          <el-select v-model="form.education" placeholder="请选择学历">
            <el-option
                v-for="dict in education_option"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="工作单位" prop="company">
          <el-input v-model="form.company" placeholder="请输入工作单位"/>
        </el-form-item>
        <el-form-item label="二寸照片URL">
          <image-upload v-model="form.photoUrl"/>
        </el-form-item>
        <el-form-item label="海姆利克实操照片">
          <image-upload v-model="form.heimlichUrl"/>
        </el-form-item>
        <el-form-item label="付款截图url">
          <image-upload v-model="form.paymentUrl"/>
        </el-form-item>
        <el-form-item label="考试截图">
          <image-upload v-model="form.examUrl"/>
        </el-form-item>
        <el-form-item label="申请类型" prop="applyType">
          <el-select v-model="form.applyType" placeholder="请选择申请类型">
            <el-option
                v-for="dict in apply_status"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="审批状态">
          <el-radio-group v-model="form.applyStatus">
            <el-radio
                v-for="dict in apply_status"
                :key="dict.value"
                :label="parseInt(dict.value)"
            >{{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="领取方式" prop="receiveType">
          <el-select v-model="form.receiveType" placeholder="请选择领取方式：SELF-自取，MAIL-邮寄">
            <el-option
                v-for="dict in receive_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="收件地址" prop="receiveAddress">
          <el-input v-model="form.receiveAddress" placeholder="请输入收件地址"/>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import {listApply, getApply, delApply, addApply, updateApply,auditApply} from "@/api/bizz/apply";


const {proxy} = getCurrentInstance();
const {
  receive_type,
  education_option,
  apply_status,
  sys_user_sex,
  apply_type,
  sys_yes_no
} = proxy.useDict('receive_type', 'education_option', 'apply_status', 'sys_user_sex', 'apply_type', 'sys_yes_no');

const applyList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

// 计算属性：所有选中项的审批状态是否都是2
const allSelectedStatus2 = computed(() => {
  return ids.value.length > 0 &&
    applyList.value.filter(item => ids.value.includes(item.id)).every(item => item.applyStatus == 2);
});

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    name: null,
    idCard: null,
    sex: null,
    applyType: null,
    applyStatus: null,
    receiveType: null,
    timeRange: [], // 新增字段：日期范围
  },
  rules: {
    name: [
      {required: true, message: "姓名不能为空", trigger: "blur"}
    ],
    idCard: [
      {required: true, message: "身份证号不能为空", trigger: "blur"}
    ],
    photoUrl: [
      {required: true, message: "二寸照片URL不能为空", trigger: "blur"}
    ],
    heimlichUrl: [
      {required: true, message: "海姆利克实操照片不能为空", trigger: "blur"}
    ],
    paymentUrl: [
      {required: true, message: "付款截图url不能为空", trigger: "blur"}
    ],
    applyType: [
      {required: true, message: "申请类型不能为空", trigger: "change"}
    ],
    applyStatus: [
      {required: true, message: "审批状态不能为空", trigger: "blur"}
    ],
    receiveType: [
      {required: true, message: "领取方式：SELF-自取，MAIL-邮寄不能为空", trigger: "change"}
    ],
    receiveAddress: [
      {required: true, message: "收件地址不能为空", trigger: "blur"}
    ],
    createBy: [
      {required: true, message: "创建人不能为空", trigger: "blur"}
    ],
  }
});

const {queryParams, form, rules} = toRefs(data);

/** 查询证件申请列表 */
function getList() {
  loading.value = true;
  let timeRange = queryParams.value.timeRange;
  queryParams.value.beginTime = timeRange?.[0] || null;
  queryParams.value.endTime = timeRange?.[1] || null;
  listApply(queryParams.value).then(response => {
    applyList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    name: null,
    idCard: null,
    phone: null,
    sex: null,
    education: null,
    company: null,
    photoUrl: null,
    heimlichUrl: null,
    paymentUrl: null,
    examUrl: null,
    applyType: null,
    applyStatus: 0,
    receiveType: null,
    addressId: null,
    receiveAddress: null,
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null
  };
  proxy.resetForm("applyRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加证件申请";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value
  getApply(_id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改证件申请";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["applyRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateApply(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addApply(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除证件申请编号为"' + _ids + '"的数据项？').then(function () {
    return delApply(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {
  });
}

/** 导出按钮操作 */
function handleExport() {
  // 调用导出接口，传入查询参数
  const {name, idCard, sex, applyStatus, receiveType, applyType} = queryParams.value;
  let timeRange = queryParams.value.timeRange;
  if (timeRange && timeRange.length === 0) {
    proxy.$modal.msgError('请选择日期范围');
    return;
  }
  proxy.download('bizz/apply/exportCretInfo', {
    name,idCard,sex,applyStatus,receiveType,applyType,
    beginTime: timeRange?.[0] || null,
    endTime: timeRange?.[1] || null,
  }, `apply_${new Date().getTime()}.xlsx`)
}

/** 审批通过/拒绝操作 */
function handleApprove(id, type) {
  // type: 'pass' or 'reject'
  const _ids = id || ids.value
  proxy.$modal.confirm(`是否确认${type === 'pass' ? '审批通过' : '审批拒绝'}证件申请？`).then(function () {
    // 调用auditApply方法，传入id和type
    return auditApply(_ids, type);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess(`${type === 'pass' ? '审批通过' : '审批拒绝'}成功`);
  }).catch(() => {});
}

getList();
</script>
